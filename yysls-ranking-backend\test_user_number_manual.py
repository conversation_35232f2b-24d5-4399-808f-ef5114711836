#!/usr/bin/env python3
"""
手动测试用户编号生成功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.user_number_generator import (
    UserNumberGenerator,
    generate_user_number,
    validate_user_number
)


def test_validation():
    """测试用户编号验证功能"""
    print("=== 测试用户编号验证功能 ===")
    
    # 测试有效格式
    valid_numbers = ["YY00001", "YY00010", "YY12345", "YY99999"]
    for number in valid_numbers:
        result = validate_user_number(number)
        print(f"验证 {number}: {'✓' if result else '✗'}")
        assert result, f"应该验证通过: {number}"
    
    # 测试无效格式
    invalid_numbers = ["", "YY0001", "YY000001", "XX00001", "YY0000a", "yy00001"]
    for number in invalid_numbers:
        result = validate_user_number(number)
        print(f"验证 {number}: {'✗' if not result else '✓'}")
        assert not result, f"应该验证失败: {number}"
    
    print("用户编号验证功能测试通过！\n")


def test_number_extraction():
    """测试数字提取功能"""
    print("=== 测试数字提取功能 ===")
    
    test_cases = [
        ("YY00001", 1),
        ("YY00010", 10),
        ("YY12345", 12345),
        ("YY99999", 99999),
        ("invalid", None),
        ("", None)
    ]
    
    for user_number, expected in test_cases:
        result = UserNumberGenerator.extract_number_from_user_number(user_number)
        print(f"提取 {user_number}: {result} (期望: {expected})")
        assert result == expected, f"用户编号 {user_number} 应该提取出 {expected}，实际得到 {result}"
    
    print("数字提取功能测试通过！\n")


def test_format_generation():
    """测试编号格式生成"""
    print("=== 测试编号格式生成 ===")
    
    test_cases = [
        (1, "YY00001"),
        (10, "YY00010"),
        (100, "YY00100"),
        (1000, "YY01000"),
        (99999, "YY99999")
    ]
    
    for number, expected in test_cases:
        formatted = f"{UserNumberGenerator.PREFIX}{number:0{UserNumberGenerator.NUMBER_LENGTH}d}"
        print(f"格式化 {number}: {formatted} (期望: {expected})")
        assert formatted == expected
        assert validate_user_number(formatted)
    
    print("编号格式生成测试通过！\n")


def test_constants():
    """测试常量定义"""
    print("=== 测试常量定义 ===")
    
    assert UserNumberGenerator.PREFIX == "YY"
    assert UserNumberGenerator.NUMBER_LENGTH == 5
    assert UserNumberGenerator.MAX_RETRIES == 10
    
    print(f"前缀: {UserNumberGenerator.PREFIX}")
    print(f"数字长度: {UserNumberGenerator.NUMBER_LENGTH}")
    print(f"最大重试次数: {UserNumberGenerator.MAX_RETRIES}")
    print("常量定义测试通过！\n")


def main():
    """主测试函数"""
    print("开始测试用户编号生成功能...\n")
    
    try:
        test_validation()
        test_number_extraction()
        test_format_generation()
        test_constants()
        
        print("🎉 所有测试通过！用户编号生成功能实现正确。")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
