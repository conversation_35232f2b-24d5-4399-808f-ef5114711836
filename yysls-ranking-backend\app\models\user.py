"""
用户模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Text, TIMESTAMP, CheckConstraint, event
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

from app.core.database import Base


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"  # 超级管理员
    ADMIN = "admin"  # 管理员
    USER = "user"    # 普通用户


class UserGender(str, Enum):
    """用户性别枚举"""
    MALE = "男"                      # 男性
    FEMALE = "女"                    # 女性
    PREFER_NOT_TO_SAY = "不愿意透露"  # 不愿透露


class User(Base):
    """用户表"""
    __tablename__ = "users"
    __table_args__ = (
        CheckConstraint('age IS NULL OR (age >= 0 AND age <= 150)', name='check_age_range'),
        CheckConstraint("gender IS NULL OR gender IN ('男', '女', '不愿意透露')", name='check_gender_values'),
        {
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci',
            'comment': '用户表'
        }
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="用户ID")

    # 基础信息
    username = Column(String(50), unique=True, index=True, nullable=True, comment="用户名")
    password_hash = Column(String(255), nullable=True, comment="密码哈希")
    nickname = Column(String(100), nullable=False, default="", comment="昵称")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    phone = Column(String(20), unique=True, index=True, nullable=True, comment="手机号")

    # 微信信息
    wechat_openid = Column(String(100), unique=True, index=True, nullable=True, comment="微信OpenID")
    wechat_unionid = Column(String(100), unique=True, index=True, nullable=True, comment="微信UnionID")

    # 角色和状态
    role = Column(String(20), default=UserRole.USER.value, nullable=False, comment="用户角色")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_verified = Column(Boolean, default=False, nullable=False, comment="是否验证")

    # 扩展信息
    bio = Column(Text, nullable=True, comment="个人简介")
    level = Column(String(50), default="江湖新人", nullable=True, comment="用户等级")
    points = Column(Integer, default=0, nullable=False, comment="积分")

    # 新增用户信息字段
    location = Column(String(200), nullable=True, comment="所在地")
    user_number = Column(String(50), unique=True, nullable=True, index=True, comment="用户编号")
    gender = Column(String(20), nullable=True, comment="性别")
    age = Column(Integer, nullable=True, comment="年龄")

    # 时间戳 - 使用MySQL的TIMESTAMP类型
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(),
                       onupdate=func.current_timestamp(), comment="更新时间")
    last_login_at = Column(TIMESTAMP, nullable=True, comment="最后登录时间")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"


# SQLAlchemy事件监听器：在创建用户后基于ID生成用户编号
@event.listens_for(User, 'after_insert')
def generate_user_number_after_insert(mapper, connection, target):
    """
    在插入用户记录后基于用户ID自动生成用户编号

    Args:
        mapper: SQLAlchemy映射器
        connection: 数据库连接
        target: 已插入的User实例
    """
    # 如果用户编号已经设置，则不重新生成
    if target.user_number:
        return

    try:
        # 导入生成器（避免循环导入）
        from app.utils.user_number_generator import UserNumberGenerator

        # 基于用户ID生成编号
        user_number = UserNumberGenerator.generate_user_number_from_id(target.id)

        # 更新用户编号
        connection.execute(
            f"UPDATE users SET user_number = '{user_number}' WHERE id = {target.id}"
        )

        # 更新内存中的对象
        target.user_number = user_number

    except Exception as e:
        # 记录错误但不阻止用户创建
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"自动生成用户编号失败: {str(e)}")
        pass
