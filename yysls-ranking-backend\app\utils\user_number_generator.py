"""
用户编号生成器

提供用户编号自动生成功能，格式为 YY + 5位数字（如：YY00001, YY00002）
支持并发安全和唯一性保证
"""
import logging
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import select, func, text
from sqlalchemy.exc import IntegrityError

logger = logging.getLogger(__name__)


class UserNumberGenerator:
    """用户编号生成器"""
    
    PREFIX = "YY"
    NUMBER_LENGTH = 5
    MAX_RETRIES = 10
    
    @classmethod
    def generate_user_number(cls, db: Session) -> str:
        """
        生成唯一的用户编号
        
        Args:
            db: 数据库会话
            
        Returns:
            str: 生成的用户编号，格式为 YY + 5位数字
            
        Raises:
            RuntimeError: 生成失败时抛出异常
        """
        from app.models.user import User  # 避免循环导入
        
        for attempt in range(cls.MAX_RETRIES):
            try:
                # 获取当前最大的用户编号
                max_number = cls._get_max_user_number(db)
                
                # 生成下一个编号
                next_number = max_number + 1
                user_number = f"{cls.PREFIX}{next_number:0{cls.NUMBER_LENGTH}d}"
                
                # 验证编号是否已存在（双重检查）
                existing_user = db.execute(
                    select(User).where(User.user_number == user_number)
                ).scalar_one_or_none()
                
                if existing_user is None:
                    logger.info(f"生成用户编号成功: {user_number}")
                    return user_number
                else:
                    logger.warning(f"用户编号已存在，重试: {user_number}")
                    continue
                    
            except Exception as e:
                logger.error(f"生成用户编号失败 (尝试 {attempt + 1}/{cls.MAX_RETRIES}): {str(e)}")
                if attempt == cls.MAX_RETRIES - 1:
                    raise RuntimeError(f"生成用户编号失败，已重试 {cls.MAX_RETRIES} 次")
                continue
        
        raise RuntimeError("生成用户编号失败，超过最大重试次数")
    
    @classmethod
    def _get_max_user_number(cls, db: Session) -> int:
        """
        获取当前最大的用户编号数字部分
        
        Args:
            db: 数据库会话
            
        Returns:
            int: 最大编号的数字部分，如果没有用户则返回0
        """
        from app.models.user import User  # 避免循环导入
        
        try:
            # 使用SQLAlchemy ORM查询，更好的跨数据库兼容性
            from app.models.user import User

            # 查询所有符合格式的用户编号
            result = db.execute(
                select(User.user_number).where(
                    User.user_number.like(f"{cls.PREFIX}%")
                )
            ).scalars().all()

            max_number = 0
            for user_number in result:
                if user_number and cls.validate_user_number(user_number):
                    number_part = user_number[len(cls.PREFIX):]
                    try:
                        current_number = int(number_part)
                        max_number = max(max_number, current_number)
                    except ValueError:
                        continue

            return max_number
            
        except Exception as e:
            logger.error(f"获取最大用户编号失败: {str(e)}")
            # 如果查询失败，从1开始
            return 0
    
    @classmethod
    def validate_user_number(cls, user_number: str) -> bool:
        """
        验证用户编号格式是否正确
        
        Args:
            user_number: 要验证的用户编号
            
        Returns:
            bool: 格式是否正确
        """
        if not user_number:
            return False
            
        if not user_number.startswith(cls.PREFIX):
            return False
            
        if len(user_number) != len(cls.PREFIX) + cls.NUMBER_LENGTH:
            return False
            
        number_part = user_number[len(cls.PREFIX):]
        if not number_part.isdigit():
            return False
            
        return True
    
    @classmethod
    def extract_number_from_user_number(cls, user_number: str) -> Optional[int]:
        """
        从用户编号中提取数字部分
        
        Args:
            user_number: 用户编号
            
        Returns:
            Optional[int]: 数字部分，如果格式不正确则返回None
        """
        if not cls.validate_user_number(user_number):
            return None
            
        number_part = user_number[len(cls.PREFIX):]
        try:
            return int(number_part)
        except ValueError:
            return None


def generate_user_number(db: Session) -> str:
    """
    生成唯一的用户编号（便捷函数）
    
    Args:
        db: 数据库会话
        
    Returns:
        str: 生成的用户编号
    """
    return UserNumberGenerator.generate_user_number(db)


def validate_user_number(user_number: str) -> bool:
    """
    验证用户编号格式（便捷函数）
    
    Args:
        user_number: 要验证的用户编号
        
    Returns:
        bool: 格式是否正确
    """
    return UserNumberGenerator.validate_user_number(user_number)
